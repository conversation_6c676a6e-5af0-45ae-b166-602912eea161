using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    public class EmailLog : IEntity
    {
        [Key]
        public int LogID { get; set; }
        public int? UserID { get; set; }
        public int? CompanyID { get; set; }
        public string EmailType { get; set; } // 'PasswordReset', 'EmailVerification', etc.
        public string ToEmail { get; set; }
        public string Subject { get; set; }
        public string Status { get; set; } // 'Sent', 'Failed', 'Pending'
        public string? ErrorMessage { get; set; }
        public DateTime? SentDate { get; set; }
        public string? MailgunMessageId { get; set; } // Mailgun tracking için
    }
}
