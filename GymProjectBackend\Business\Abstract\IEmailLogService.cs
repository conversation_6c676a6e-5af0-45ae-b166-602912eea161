using Core.Utilities.Results;
using Entities.Concrete;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IEmailLogService
    {
        /// <summary>
        /// E-posta gönderim logunu kaydeder
        /// </summary>
        Task<IResult> LogEmailAsync(int? userId, int? companyId, string emailType, string toEmail, string subject, string status, string errorMessage = null, string messageId = null);

        /// <summary>
        /// Kullanıcının e-posta geçmişini getirir
        /// </summary>
        Task<IDataResult<List<EmailLog>>> GetUserEmailLogsAsync(int userId);

        /// <summary>
        /// Şirketin e-posta geçmişini getirir
        /// </summary>
        Task<IDataResult<List<EmailLog>>> GetCompanyEmailLogsAsync(int companyId);

        /// <summary>
        /// E-posta türüne göre logları getirir
        /// </summary>
        Task<IDataResult<List<EmailLog>>> GetEmailLogsByTypeAsync(string emailType);

        /// <summary>
        /// Başarısız e-posta gönderimlerini getirir
        /// </summary>
        Task<IDataResult<List<EmailLog>>> GetFailedEmailsAsync();
    }
}
