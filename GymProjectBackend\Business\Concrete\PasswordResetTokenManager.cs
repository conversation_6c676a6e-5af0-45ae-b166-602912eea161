using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Microsoft.AspNetCore.Http;
using System;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class PasswordResetTokenManager : IPasswordResetTokenService
    {
        private readonly IPasswordResetTokenDal _passwordResetTokenDal;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private const int TOKEN_EXPIRY_HOURS = 1; // 1 saat
        private const int MAX_TOKENS_PER_DAY = 5; // Günde maksimum 5 token

        public PasswordResetTokenManager(IPasswordResetTokenDal passwordResetTokenDal, IHttpContextAccessor httpContextAccessor)
        {
            _passwordResetTokenDal = passwordResetTokenDal;
            _httpContextAccessor = httpContextAccessor;
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<PasswordResetToken>> CreateTokenAsync(int userId, int? companyId = null)
        {
            try
            {
                // Rate limiting kontrolü
                var rateLimitCheck = await CheckTokenRequestLimitAsync(userId);
                if (!rateLimitCheck.Success)
                {
                    return new ErrorDataResult<PasswordResetToken>(null, rateLimitCheck.Message);
                }

                // Kullanıcının mevcut aktif tokenlarını iptal et
                await InvalidateUserTokensAsync(userId);

                // Yeni token oluştur
                var token = GenerateSecureToken();
                var expiryDate = DateTime.Now.AddHours(TOKEN_EXPIRY_HOURS);

                var passwordResetToken = new PasswordResetToken
                {
                    UserID = userId,
                    Token = token,
                    ExpiryDate = expiryDate,
                    IsUsed = false,
                    CreatedDate = DateTime.Now,
                    CompanyID = companyId,
                    IPAddress = GetClientIPAddress(),
                    UserAgent = GetUserAgent()
                };

                await _passwordResetTokenDal.AddAsync(passwordResetToken);

                return new SuccessDataResult<PasswordResetToken>(passwordResetToken, "Şifre sıfırlama token'ı başarıyla oluşturuldu.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<PasswordResetToken>(null, $"Token oluşturulamadı: {ex.Message}");
            }
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<PasswordResetToken>> ValidateTokenAsync(string token)
        {
            try
            {
                if (string.IsNullOrEmpty(token))
                {
                    return new ErrorDataResult<PasswordResetToken>(null, "Token boş olamaz.");
                }

                var passwordResetToken = await _passwordResetTokenDal.GetByTokenAsync(token);

                if (passwordResetToken == null)
                {
                    return new ErrorDataResult<PasswordResetToken>(null, "Geçersiz token.");
                }

                if (passwordResetToken.IsUsed)
                {
                    return new ErrorDataResult<PasswordResetToken>(null, "Bu token daha önce kullanılmış.");
                }

                if (passwordResetToken.ExpiryDate < DateTime.Now)
                {
                    return new ErrorDataResult<PasswordResetToken>(null, "Token'ın süresi dolmuş. Yeni bir şifre sıfırlama talebi oluşturun.");
                }

                return new SuccessDataResult<PasswordResetToken>(passwordResetToken, "Token geçerli.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<PasswordResetToken>(null, $"Token doğrulanamadı: {ex.Message}");
            }
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> MarkTokenAsUsedAsync(string token)
        {
            try
            {
                var result = await _passwordResetTokenDal.MarkTokenAsUsedAsync(token);
                if (result)
                {
                    return new SuccessResult("Token başarıyla kullanıldı olarak işaretlendi.");
                }
                else
                {
                    return new ErrorResult("Token işaretlenemedi.");
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Token işaretlenemedi: {ex.Message}");
            }
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> InvalidateUserTokensAsync(int userId)
        {
            try
            {
                await _passwordResetTokenDal.InvalidateUserTokensAsync(userId);
                return new SuccessResult("Kullanıcının token'ları başarıyla iptal edildi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Token'lar iptal edilemedi: {ex.Message}");
            }
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> CleanupExpiredTokensAsync()
        {
            try
            {
                await _passwordResetTokenDal.CleanupExpiredTokensAsync();
                return new SuccessResult("Süresi dolmuş token'lar başarıyla temizlendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Token temizleme işlemi başarısız: {ex.Message}");
            }
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> CheckTokenRequestLimitAsync(int userId)
        {
            try
            {
                var tokenCount = await _passwordResetTokenDal.GetUserTokenCountInLast24HoursAsync(userId);
                
                if (tokenCount >= MAX_TOKENS_PER_DAY)
                {
                    return new ErrorResult($"24 saat içinde en fazla {MAX_TOKENS_PER_DAY} şifre sıfırlama talebi oluşturabilirsiniz. Lütfen daha sonra tekrar deneyin.");
                }

                return new SuccessResult();
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Rate limit kontrolü yapılamadı: {ex.Message}");
            }
        }

        private string GenerateSecureToken()
        {
            using (var rng = RandomNumberGenerator.Create())
            {
                var bytes = new byte[32]; // 256 bit
                rng.GetBytes(bytes);
                return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
            }
        }

        private string GetClientIPAddress()
        {
            try
            {
                return _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "unknown";
            }
            catch
            {
                return "unknown";
            }
        }

        private string GetUserAgent()
        {
            try
            {
                return _httpContextAccessor.HttpContext?.Request?.Headers["User-Agent"].ToString() ?? "unknown";
            }
            catch
            {
                return "unknown";
            }
        }
    }
}
