using Core.Entities;
using System.ComponentModel.DataAnnotations;

namespace Entities.DTOs
{
    public class ForgotPasswordDto : IDto
    {
        [Required(ErrorMessage = "E-posta adresi gereklidir")]
        [EmailAddress(ErrorMessage = "Geçerli bir e-posta adresi giriniz")]
        public string Email { get; set; }
    }

    public class ResetPasswordDto : IDto
    {
        [Required(ErrorMessage = "Token gereklidir")]
        public string Token { get; set; }

        [Required(ErrorMessage = "Yeni şifre gereklidir")]
        [MinLength(6, ErrorMessage = "Şifre en az 6 karakter olmalıdır")]
        public string NewPassword { get; set; }

        [Required(ErrorMessage = "Şifre tekrarı gereklidir")]
        [Compare("NewPassword", ErrorMessage = "Şifreler eşleşmiyor")]
        public string ConfirmPassword { get; set; }
    }

    public class PasswordResetRequestDto : IDto
    {
        public string Email { get; set; }
        public string Token { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string ResetUrl { get; set; }
    }
}
