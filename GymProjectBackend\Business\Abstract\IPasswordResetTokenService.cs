using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IPasswordResetTokenService
    {
        /// <summary>
        /// Yeni password reset token oluşturur
        /// </summary>
        Task<IDataResult<PasswordResetToken>> CreateTokenAsync(int userId, int? companyId = null);

        /// <summary>
        /// Token'ı doğrular ve geçerliliğini kontrol eder
        /// </summary>
        Task<IDataResult<PasswordResetToken>> ValidateTokenAsync(string token);

        /// <summary>
        /// Token'ı kullanıldı olarak işaretler
        /// </summary>
        Task<IResult> MarkTokenAsUsedAsync(string token);

        /// <summary>
        /// Kullanıcının aktif token'larını iptal eder
        /// </summary>
        Task<IResult> InvalidateUserTokensAsync(int userId);

        /// <summary>
        /// <PERSON><PERSON>resi dolmuş token'ları temizler
        /// </summary>
        Task<IResult> CleanupExpiredTokensAsync();

        /// <summary>
        /// Kullanıcının son 24 saatte kaç token talep ettiğini kontrol eder (Rate limiting)
        /// </summary>
        Task<IResult> CheckTokenRequestLimitAsync(int userId);
    }
}
