using Core.DataAccess;
using Entities.Concrete;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IPasswordResetTokenDal : IEntityRepository<PasswordResetToken>
    {
        /// <summary>
        /// Token string'i ile PasswordResetToken getirir
        /// </summary>
        Task<PasswordResetToken> GetByTokenAsync(string token);

        /// <summary>
        /// Token'ı kullanıldı olarak işaretler
        /// </summary>
        Task<bool> MarkTokenAsUsedAsync(string token);

        /// <summary>
        /// Kullanıcının tüm aktif token'larını iptal eder
        /// </summary>
        Task InvalidateUserTokensAsync(int userId);

        /// <summary>
        /// Süresi dolmuş token'ları temizler
        /// </summary>
        Task CleanupExpiredTokensAsync();

        /// <summary>
        /// Kullanıcının son 24 saatteki token sayısını getirir
        /// </summary>
        Task<int> GetUserTokenCountInLast24HoursAsync(int userId);

        /// <summary>
        /// Async Add metodu
        /// </summary>
        Task AddAsync(PasswordResetToken entity);
    }
}
