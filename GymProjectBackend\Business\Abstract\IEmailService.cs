using Core.Utilities.Email;
using Core.Utilities.Results;
using Entities.DTOs;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IEmailService
    {
        /// <summary>
        /// Ş<PERSON>re sıfırlama e-postası gönderir
        /// </summary>
        Task<IResult> SendPasswordResetEmailAsync(PasswordResetRequestDto request);

        /// <summary>
        /// E-posta doğrulama e-postası gönderir (İleride kullanım için)
        /// </summary>
        Task<IResult> SendEmailVerificationAsync(string email, string verificationUrl);

        /// <summary>
        /// Genel e-posta gönderme metodu
        /// </summary>
        Task<EmailResult> SendEmailAsync(string toEmail, string subject, string htmlContent, string textContent = null);

        /// <summary>
        /// E-posta template'ini render eder
        /// </summary>
        string RenderPasswordResetTemplate(string userName, string resetUrl, string companyName);

        /// <summary>
        /// E-posta gönderim durumunu loglar
        /// </summary>
        Task LogEmailAsync(int? userId, int? companyId, string emailType, string toEmail, string subject, string status, string errorMessage = null, string messageId = null);
    }
}
