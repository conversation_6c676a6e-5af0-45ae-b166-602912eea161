using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfPasswordResetTokenDal : EfEntityRepositoryBase<PasswordResetToken, GymContext>, IPasswordResetTokenDal
    {
        // Constructor injection (Scalability için)
        public EfPasswordResetTokenDal(GymContext context) : base(context)
        {
        }

        public async Task<PasswordResetToken> GetByTokenAsync(string token)
        {
            return await _context.Set<PasswordResetToken>()
                .FirstOrDefaultAsync(t => t.Token == token);
        }

        public async Task<bool> MarkTokenAsUsedAsync(string token)
        {
            var tokenEntity = await _context.Set<PasswordResetToken>()
                .FirstOrDefaultAsync(t => t.Token == token);

            if (tokenEntity != null)
            {
                tokenEntity.IsUsed = true;
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        public async Task InvalidateUserTokensAsync(int userId)
        {
            var userTokens = await _context.Set<PasswordResetToken>()
                .Where(t => t.UserID == userId && !t.IsUsed && t.ExpiryDate > DateTime.Now)
                .ToListAsync();

            foreach (var token in userTokens)
            {
                token.IsUsed = true;
            }

            await _context.SaveChangesAsync();
        }

        public async Task CleanupExpiredTokensAsync()
        {
            var expiredTokens = await _context.Set<PasswordResetToken>()
                .Where(t => t.ExpiryDate < DateTime.Now.AddHours(-24))
                .ToListAsync();

            _context.Set<PasswordResetToken>().RemoveRange(expiredTokens);
            await _context.SaveChangesAsync();
        }

        public async Task<int> GetUserTokenCountInLast24HoursAsync(int userId)
        {
            var yesterday = DateTime.Now.AddHours(-24);

            return await _context.Set<PasswordResetToken>()
                .CountAsync(t => t.UserID == userId && t.CreatedDate >= yesterday);
        }

        public async Task AddAsync(PasswordResetToken entity)
        {
            entity.CreatedDate = DateTime.Now;
            await _context.Set<PasswordResetToken>().AddAsync(entity);
            await _context.SaveChangesAsync();
        }
    }
}
