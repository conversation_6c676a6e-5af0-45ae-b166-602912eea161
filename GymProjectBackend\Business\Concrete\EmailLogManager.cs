using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class EmailLogManager : IEmailLogService
    {
        private readonly IEmailLogDal _emailLogDal;

        public EmailLogManager(IEmailLogDal emailLogDal)
        {
            _emailLogDal = emailLogDal;
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IResult> LogEmailAsync(int? userId, int? companyId, string emailType, string toEmail, string subject, string status, string errorMessage = null, string messageId = null)
        {
            try
            {
                var emailLog = new EmailLog
                {
                    UserID = userId,
                    CompanyID = companyId,
                    EmailType = emailType,
                    ToEmail = toEmail,
                    Subject = subject,
                    Status = status,
                    ErrorMessage = errorMessage,
                    SentDate = DateTime.Now,
                    MailgunMessageId = messageId
                };

                await _emailLogDal.AddAsync(emailLog);
                return new SuccessResult("E-posta log kaydı başarıyla oluşturuldu.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"E-posta log kaydı oluşturulamadı: {ex.Message}");
            }
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<List<EmailLog>>> GetUserEmailLogsAsync(int userId)
        {
            try
            {
                var logs = await _emailLogDal.GetUserEmailLogsAsync(userId);
                return new SuccessDataResult<List<EmailLog>>(logs, "Kullanıcı e-posta logları başarıyla getirildi.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<EmailLog>>(null, $"E-posta logları getirilemedi: {ex.Message}");
            }
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<List<EmailLog>>> GetCompanyEmailLogsAsync(int companyId)
        {
            try
            {
                var logs = await _emailLogDal.GetCompanyEmailLogsAsync(companyId);
                return new SuccessDataResult<List<EmailLog>>(logs, "Şirket e-posta logları başarıyla getirildi.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<EmailLog>>(null, $"E-posta logları getirilemedi: {ex.Message}");
            }
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<List<EmailLog>>> GetEmailLogsByTypeAsync(string emailType)
        {
            try
            {
                var logs = await _emailLogDal.GetEmailLogsByTypeAsync(emailType);
                return new SuccessDataResult<List<EmailLog>>(logs, "E-posta türü logları başarıyla getirildi.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<EmailLog>>(null, $"E-posta logları getirilemedi: {ex.Message}");
            }
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public async Task<IDataResult<List<EmailLog>>> GetFailedEmailsAsync()
        {
            try
            {
                var logs = await _emailLogDal.GetFailedEmailsAsync();
                return new SuccessDataResult<List<EmailLog>>(logs, "Başarısız e-posta logları başarıyla getirildi.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<EmailLog>>(null, $"E-posta logları getirilemedi: {ex.Message}");
            }
        }
    }
}
