-- Password Reset Token Sistemi için Veritabanı Tabloları
-- GymKod Pro - Çok Kiracılı E-posta Sistemi

-- 1. Password Reset Tokens Tablosu
CREATE TABLE PasswordResetTokens (
    TokenID int IDENTITY(1,1) PRIMARY KEY,
    UserID int NOT NULL,
    Token nvarchar(255) NOT NULL UNIQUE,
    ExpiryDate datetime NOT NULL,
    IsUsed bit DEFAULT 0,
    CreatedDate datetime DEFAULT GETDATE(),
    CompanyID int NULL, -- Multi-tenant support için
    IPAddress nvarchar(45) NULL, -- Güvenlik için IP tracking
    UserAgent nvarchar(500) NULL, -- Güvenlik için browser tracking
    FOREIGN KEY (UserID) REFERENCES Users(UserID),
    INDEX IX_PasswordResetTokens_Token (Token),
    INDEX IX_PasswordResetTokens_UserID (UserID),
    INDEX IX_PasswordResetTokens_ExpiryDate (ExpiryDate)
);

-- 2. Email Verification Tablosu (İleride kullanım için)
CREATE TABLE EmailVerifications (
    VerificationID int IDENTITY(1,1) PRIMARY KEY,
    UserID int NOT NULL,
    Email nvarchar(255) NOT NULL,
    Token nvarchar(255) NOT NULL UNIQUE,
    ExpiryDate datetime NOT NULL,
    IsVerified bit DEFAULT 0,
    CreatedDate datetime DEFAULT GETDATE(),
    CompanyID int NULL, -- Multi-tenant support için
    FOREIGN KEY (UserID) REFERENCES Users(UserID),
    INDEX IX_EmailVerifications_Token (Token),
    INDEX IX_EmailVerifications_UserID (UserID),
    INDEX IX_EmailVerifications_Email (Email)
);

-- 3. Email Logs Tablosu (E-posta gönderim takibi için)
CREATE TABLE EmailLogs (
    LogID int IDENTITY(1,1) PRIMARY KEY,
    UserID int NULL,
    CompanyID int NULL,
    EmailType nvarchar(50) NOT NULL, -- 'PasswordReset', 'EmailVerification', etc.
    ToEmail nvarchar(255) NOT NULL,
    Subject nvarchar(500) NOT NULL,
    Status nvarchar(50) NOT NULL, -- 'Sent', 'Failed', 'Pending'
    ErrorMessage nvarchar(1000) NULL,
    SentDate datetime DEFAULT GETDATE(),
    MailgunMessageId nvarchar(255) NULL, -- Mailgun tracking için
    INDEX IX_EmailLogs_UserID (UserID),
    INDEX IX_EmailLogs_CompanyID (CompanyID),
    INDEX IX_EmailLogs_EmailType (EmailType),
    INDEX IX_EmailLogs_Status (Status),
    INDEX IX_EmailLogs_SentDate (SentDate)
);

-- 4. Temizlik için stored procedure (Eski tokenları temizlemek için)
CREATE PROCEDURE CleanupExpiredTokens
AS
BEGIN
    -- 24 saatten eski expired tokenları sil
    DELETE FROM PasswordResetTokens 
    WHERE ExpiryDate < DATEADD(HOUR, -24, GETDATE());
    
    -- 7 günden eski email verification tokenları sil
    DELETE FROM EmailVerifications 
    WHERE ExpiryDate < DATEADD(DAY, -7, GETDATE());
    
    -- 30 günden eski email logları sil (opsiyonel)
    DELETE FROM EmailLogs 
    WHERE SentDate < DATEADD(DAY, -30, GETDATE());
END;

-- 5. Rate limiting için ek indeksler (mevcut sisteminizle uyumlu)
CREATE INDEX IX_PasswordResetTokens_CreatedDate_UserID 
ON PasswordResetTokens (CreatedDate, UserID);

CREATE INDEX IX_EmailLogs_SentDate_ToEmail 
ON EmailLogs (SentDate, ToEmail);
