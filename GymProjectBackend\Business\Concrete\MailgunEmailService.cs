using Business.Abstract;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Email;
using Core.Utilities.Results;
using Entities.DTOs;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class MailgunEmailService : IEmailService
    {
        private readonly EmailSettings _emailSettings;
        private readonly IConfiguration _configuration;
        private readonly IEmailLogService _emailLogService;

        public MailgunEmailService(IConfiguration configuration, IEmailLogService emailLogService)
        {
            _configuration = configuration;
            _emailLogService = emailLogService;
            
            // Environment'a göre email ayarlarını al
            var environment = _configuration["Environment"] ?? "dev";
            _emailSettings = _configuration.GetSection($"EmailSettings:{environment}").Get<EmailSettings>();
            
            if (_emailSettings == null)
            {
                throw new InvalidOperationException($"'{environment}' environment'ı için email ayarları bulunamadı!");
            }
        }

        [LogAspect]
        [PerformanceAspect(5)]
        public async Task<IResult> SendPasswordResetEmailAsync(PasswordResetRequestDto request)
        {
            try
            {
                var subject = "GymKod Pro - Şifre Sıfırlama";
                var htmlContent = RenderPasswordResetTemplate(request.Email, request.ResetUrl, "GymKod Pro");
                var textContent = $"Şifrenizi sıfırlamak için şu bağlantıya tıklayın: {request.ResetUrl}";

                var result = await SendEmailAsync(request.Email, subject, htmlContent, textContent);

                if (result.Success)
                {
                    await LogEmailAsync(null, null, "PasswordReset", request.Email, subject, "Sent", null, result.MessageId);
                    return new SuccessResult("Şifre sıfırlama e-postası başarıyla gönderildi.");
                }
                else
                {
                    await LogEmailAsync(null, null, "PasswordReset", request.Email, subject, "Failed", result.ErrorDetails);
                    return new ErrorResult($"E-posta gönderilemedi: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                await LogEmailAsync(null, null, "PasswordReset", request.Email, "Şifre Sıfırlama", "Failed", ex.Message);
                return new ErrorResult($"E-posta gönderimi sırasında hata oluştu: {ex.Message}");
            }
        }

        [LogAspect]
        [PerformanceAspect(5)]
        public async Task<IResult> SendEmailVerificationAsync(string email, string verificationUrl)
        {
            try
            {
                var subject = "GymKod Pro - E-posta Doğrulama";
                var htmlContent = RenderEmailVerificationTemplate(email, verificationUrl);
                var textContent = $"E-postanızı doğrulamak için şu bağlantıya tıklayın: {verificationUrl}";

                var result = await SendEmailAsync(email, subject, htmlContent, textContent);

                if (result.Success)
                {
                    await LogEmailAsync(null, null, "EmailVerification", email, subject, "Sent", null, result.MessageId);
                    return new SuccessResult("E-posta doğrulama e-postası başarıyla gönderildi.");
                }
                else
                {
                    await LogEmailAsync(null, null, "EmailVerification", email, subject, "Failed", result.ErrorDetails);
                    return new ErrorResult($"E-posta gönderilemedi: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                await LogEmailAsync(null, null, "EmailVerification", email, "E-posta Doğrulama", "Failed", ex.Message);
                return new ErrorResult($"E-posta gönderimi sırasında hata oluştu: {ex.Message}");
            }
        }

        [LogAspect]
        [PerformanceAspect(5)]
        public async Task<EmailResult> SendEmailAsync(string toEmail, string subject, string htmlContent, string textContent = null)
        {
            try
            {
                var client = new RestClient(_emailSettings.BaseUrl);
                var request = new RestRequest($"/{_emailSettings.Domain}/messages", Method.Post);

                // Mailgun API authentication
                request.AddHeader("Authorization", $"Basic {Convert.ToBase64String(Encoding.ASCII.GetBytes($"api:{_emailSettings.ApiKey}"))}");

                // E-posta parametreleri
                request.AddParameter("from", $"{_emailSettings.FromName} <{_emailSettings.FromEmail}>");
                request.AddParameter("to", toEmail);
                request.AddParameter("subject", subject);
                request.AddParameter("html", htmlContent);
                
                if (!string.IsNullOrEmpty(textContent))
                {
                    request.AddParameter("text", textContent);
                }

                // Mailgun tracking options
                request.AddParameter("o:tracking", "yes");
                request.AddParameter("o:tracking-clicks", "yes");
                request.AddParameter("o:tracking-opens", "yes");

                var response = await client.ExecuteAsync(request);

                if (response.IsSuccessful)
                {
                    var mailgunResponse = JsonConvert.DeserializeObject<MailgunResponse>(response.Content);
                    return new EmailResult
                    {
                        Success = true,
                        Message = "E-posta başarıyla gönderildi",
                        MessageId = mailgunResponse?.Id
                    };
                }
                else
                {
                    return new EmailResult
                    {
                        Success = false,
                        Message = "E-posta gönderilemedi",
                        ErrorDetails = $"HTTP {response.StatusCode}: {response.Content}"
                    };
                }
            }
            catch (Exception ex)
            {
                return new EmailResult
                {
                    Success = false,
                    Message = "E-posta gönderimi sırasında hata oluştu",
                    ErrorDetails = ex.Message
                };
            }
        }

        public string RenderPasswordResetTemplate(string userName, string resetUrl, string companyName)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Şifre Sıfırlama</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
        .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }}
        .button {{ display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
        .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 12px; }}
        .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>{companyName}</h1>
            <h2>Şifre Sıfırlama Talebi</h2>
        </div>
        <div class='content'>
            <p>Merhaba,</p>
            <p>Hesabınız için şifre sıfırlama talebinde bulundunuz. Şifrenizi sıfırlamak için aşağıdaki butona tıklayın:</p>

            <div style='text-align: center;'>
                <a href='{resetUrl}' class='button'>Şifremi Sıfırla</a>
            </div>

            <div class='warning'>
                <strong>Önemli:</strong>
                <ul>
                    <li>Bu bağlantı 1 saat geçerlidir</li>
                    <li>Bağlantı sadece bir kez kullanılabilir</li>
                    <li>Bu talebi siz yapmadıysanız, bu e-postayı görmezden gelin</li>
                </ul>
            </div>

            <p>Eğer buton çalışmıyorsa, aşağıdaki bağlantıyı kopyalayıp tarayıcınıza yapıştırın:</p>
            <p style='word-break: break-all; background: #f0f0f0; padding: 10px; border-radius: 5px;'>{resetUrl}</p>
        </div>
        <div class='footer'>
            <p>Bu e-posta otomatik olarak gönderilmiştir. Lütfen yanıtlamayın.</p>
            <p>&copy; 2024 {companyName}. Tüm hakları saklıdır.</p>
        </div>
    </div>
</body>
</html>";
        }

        private string RenderEmailVerificationTemplate(string email, string verificationUrl)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>E-posta Doğrulama</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
        .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }}
        .button {{ display: inline-block; background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
        .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>GymKod Pro</h1>
            <h2>E-posta Doğrulama</h2>
        </div>
        <div class='content'>
            <p>Merhaba,</p>
            <p>E-posta adresinizi doğrulamak için aşağıdaki butona tıklayın:</p>

            <div style='text-align: center;'>
                <a href='{verificationUrl}' class='button'>E-postamı Doğrula</a>
            </div>

            <p>Bu bağlantı 24 saat geçerlidir.</p>
        </div>
        <div class='footer'>
            <p>Bu e-posta otomatik olarak gönderilmiştir. Lütfen yanıtlamayın.</p>
            <p>&copy; 2024 GymKod Pro. Tüm hakları saklıdır.</p>
        </div>
    </div>
</body>
</html>";
        }

        public async Task LogEmailAsync(int? userId, int? companyId, string emailType, string toEmail, string subject, string status, string errorMessage = null, string messageId = null)
        {
            try
            {
                await _emailLogService.LogEmailAsync(userId, companyId, emailType, toEmail, subject, status, errorMessage, messageId);
            }
            catch (Exception ex)
            {
                // Email log hatası ana işlemi etkilememelidir
                System.Diagnostics.Debug.WriteLine($"Email log error: {ex.Message}");
            }
        }
    }
}