using Business.Abstract;
using Entities.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PasswordResetController : ControllerBase
    {
        private readonly IAuthService _authService;

        public PasswordResetController(IAuthService authService)
        {
            _authService = authService;
        }

        /// <summary>
        /// Şifre sıfırlama e-postası gönderir
        /// </summary>
        [HttpPost("forgot-password")]
        public async Task<ActionResult> ForgotPassword([FromBody] ForgotPasswordDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { success = false, message = "Geçersiz e-posta adresi" });
            }

            var result = await _authService.ForgotPasswordAsync(request.Email);

            if (result.Success)
            {
                return Ok(new
                {
                    success = true,
                    message = result.Message
                });
            }

            return BadRequest(new
            {
                success = false,
                message = result.Message
            });
        }

        /// <summary>
        /// Şifre sıfırlama token'ını doğrular
        /// </summary>
        [HttpGet("validate-token")]
        public async Task<ActionResult> ValidateToken([Required] string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                return BadRequest(new { success = false, message = "Token gereklidir" });
            }

            var result = await _authService.ValidatePasswordResetTokenAsync(token);

            if (result.Success)
            {
                return Ok(new
                {
                    success = true,
                    message = "Token geçerli"
                });
            }

            return BadRequest(new
            {
                success = false,
                message = result.Message
            });
        }

        /// <summary>
        /// Şifreyi sıfırlar
        /// </summary>
        [HttpPost("reset-password")]
        public async Task<ActionResult> ResetPassword([FromBody] ResetPasswordDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { success = false, message = "Geçersiz veri" });
            }

            var result = await _authService.ResetPasswordAsync(request.Token, request.NewPassword);

            if (result.Success)
            {
                return Ok(new
                {
                    success = true,
                    message = result.Message
                });
            }

            return BadRequest(new
            {
                success = false,
                message = result.Message
            });
        }
    }
}
