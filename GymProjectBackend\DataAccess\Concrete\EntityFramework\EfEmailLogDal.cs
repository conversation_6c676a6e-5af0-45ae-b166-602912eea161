using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfEmailLogDal : EfEntityRepositoryBase<EmailLog, GymContext>, IEmailLogDal
    {
        // Constructor injection (Scalability için)
        public EfEmailLogDal(GymContext context) : base(context)
        {
        }

        public async Task<List<EmailLog>> GetUserEmailLogsAsync(int userId)
        {
            return await _context.Set<EmailLog>()
                .Where(e => e.UserID == userId)
                .OrderByDescending(e => e.SentDate)
                .ToListAsync();
        }

        public async Task<List<EmailLog>> GetCompanyEmailLogsAsync(int companyId)
        {
            return await _context.Set<EmailLog>()
                .Where(e => e.CompanyID == companyId)
                .OrderByDescending(e => e.SentDate)
                .ToListAsync();
        }

        public async Task<List<EmailLog>> GetEmailLogsByTypeAsync(string emailType)
        {
            return await _context.Set<EmailLog>()
                .Where(e => e.EmailType == emailType)
                .OrderByDescending(e => e.SentDate)
                .ToListAsync();
        }

        public async Task<List<EmailLog>> GetFailedEmailsAsync()
        {
            return await _context.Set<EmailLog>()
                .Where(e => e.Status == "Failed")
                .OrderByDescending(e => e.SentDate)
                .ToListAsync();
        }

        public async Task AddAsync(EmailLog entity)
        {
            entity.SentDate = DateTime.Now;
            await _context.Set<EmailLog>().AddAsync(entity);
            await _context.SaveChangesAsync();
        }
    }
}
