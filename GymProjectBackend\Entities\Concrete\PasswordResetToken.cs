using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    public class PasswordResetToken : IEntity
    {
        [Key]
        public int TokenID { get; set; }
        public int UserID { get; set; }
        public string Token { get; set; }
        public DateTime ExpiryDate { get; set; }
        public bool IsUsed { get; set; }
        public DateTime? CreatedDate { get; set; }
        public int? CompanyID { get; set; } // Multi-tenant support
        public string? IPAddress { get; set; } // Güvenlik için
        public string? UserAgent { get; set; } // Güvenlik için
    }
}
