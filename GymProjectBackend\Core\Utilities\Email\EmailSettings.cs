namespace Core.Utilities.Email
{
    public class EmailSettings
    {
        public string Provider { get; set; } // "Mailgun", "SendGrid", "AmazonSES"
        public string ApiKey { get; set; }
        public string Domain { get; set; }
        public string BaseUrl { get; set; }
        public string FromEmail { get; set; }
        public string FromName { get; set; }
    }

    public class MailgunResponse
    {
        public string Id { get; set; }
        public string Message { get; set; }
    }

    public class EmailResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string MessageId { get; set; }
        public string ErrorDetails { get; set; }
    }
}
