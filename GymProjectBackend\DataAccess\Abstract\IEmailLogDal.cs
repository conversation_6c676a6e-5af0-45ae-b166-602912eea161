using Core.DataAccess;
using Entities.Concrete;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IEmailLogDal : IEntityRepository<EmailLog>
    {
        /// <summary>
        /// Kullanıcının e-posta loglarını getirir
        /// </summary>
        Task<List<EmailLog>> GetUserEmailLogsAsync(int userId);

        /// <summary>
        /// Şirketin e-posta loglarını getirir
        /// </summary>
        Task<List<EmailLog>> GetCompanyEmailLogsAsync(int companyId);

        /// <summary>
        /// E-posta türüne göre logları getirir
        /// </summary>
        Task<List<EmailLog>> GetEmailLogsByTypeAsync(string emailType);

        /// <summary>
        /// Başarısız e-posta gönderimlerini getirir
        /// </summary>
        Task<List<EmailLog>> GetFailedEmailsAsync();

        /// <summary>
        /// Async Add metodu
        /// </summary>
        Task AddAsync(EmailLog entity);
    }
}
