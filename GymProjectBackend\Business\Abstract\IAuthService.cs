﻿using Core.Entities.Concrete;
using Core.Utilities.Results;
using Core.Utilities.Security.JWT;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace Business.Abstract
{
    public interface IAuthService
    {
        IDataResult<User> Register(UserForRegisterDto userForRegisterDto, string password);
        IDataResult<User> RegisterMember(MemberForRegisterDto memberForRegisterDto, string password);
        IDataResult<User> Login(UserForLoginDto userForLoginDto, string deviceInfo);
        IResult UserExists(string email);
        IDataResult<AccessToken> CreateAccessToken(User user, string deviceInfo);
        IDataResult<AccessToken> CreateAccessTokenWithRefreshToken(string refreshToken, string ipAddress, string deviceInfo);
        IResult RevokeRefreshToken(string refreshToken);
        IResult RevokeAllDevices(int userId);
        IResult RevokeDevice(int deviceId);
        IDataResult<List<UserDeviceDto>> GetUserDevices(int userId);
        IDataResult<AccessToken> ChangeCompany(int userId, int companyId, string deviceInfo);
        IResult ChangePassword(int userId, string currentPassword, string newPassword);

        // Şifre sıfırlama metodları
        Task<IResult> ForgotPasswordAsync(string email);
        Task<IResult> ResetPasswordAsync(string token, string newPassword);
        Task<IResult> ValidatePasswordResetTokenAsync(string token);
        IDataResult<bool> CheckPasswordChangeRequired(int userId);

        // Rate Limiting Methods
        string GenerateDeviceFingerprint(string ipAddress, string userAgent, string deviceInfo);
        IDataResult<int> GetRemainingLoginBanTime(string ipAddress, string deviceFingerprint);
        IDataResult<int> GetRemainingRegisterBanTime(string ipAddress);
    }
}
